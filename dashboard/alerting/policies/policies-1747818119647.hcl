resource "grafana_notification_policy" "notification_policy_1" {
  contact_point = "NeoX-Node"
  group_by      = ["grafana_folder", "alertname"]

  policy {
    contact_point = "NeoX-Node"

    matcher {
      label = "mon_type"
      match = "="
      value = "node"
    }

    continue        = true
    group_wait      = "30s"
    group_interval  = "3m"
    repeat_interval = "4h"
  }
  policy {
    contact_point = "NeoX-Status"

    matcher {
      label = "mon_type"
      match = "="
      value = "status"
    }

    continue        = true
    group_wait      = "30s"
    group_interval  = "3m"
    repeat_interval = "4h"
  }
  policy {
    contact_point = "NeoX-MongoDB"

    matcher {
      label = "mon_type"
      match = "="
      value = "mongo"
    }

    continue        = true
    group_wait      = "30s"
    group_interval  = "3m"
    repeat_interval = "4h"
  }
  policy {
    contact_point = "NeoX-Redis"

    matcher {
      label = "mon_type"
      match = "="
      value = "redis"
    }

    continue        = true
    group_wait      = "30s"
    group_interval  = "3m"
    repeat_interval = "4h"
  }
}
