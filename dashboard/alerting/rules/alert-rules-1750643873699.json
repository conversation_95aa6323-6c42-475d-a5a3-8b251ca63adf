{"apiVersion": 1, "groups": [{"orgId": 1, "name": "node", "folder": "Alerting", "interval": "1m", "rules": [{"uid": "c72eaf97-7377-47cb-afc9-77b90a1d210a", "title": "Linux Nodes Memory Usage", "condition": "C", "data": [{"refId": "A", "relativeTimeRange": {"from": 10800, "to": 0}, "datasourceUid": "d31c6998-358e-44d3-8542-ba13b1d024a5", "model": {"datasource": {"type": "prometheus", "uid": "d31c6998-358e-44d3-8542-ba13b1d024a5"}, "editorMode": "code", "expr": "(100 * (node_memory_MemTotal_bytes{hostname=~\"(.+)\"} - node_memory_MemFree_bytes{hostname=~\"(.+)\"} - node_memory_Buffers_bytes{hostname=~\"(.+)\"} - node_memory_Cached_bytes{hostname=~\"(.+)\"}) / node_memory_MemTotal_bytes{hostname=~\"(.+)\"}) / 100", "format": "time_series", "instant": false, "interval": "", "intervalFactor": 2, "intervalMs": 15000, "legendFormat": "{{hostname}} | {{instance}}", "maxDataPoints": 43200, "range": true, "refId": "A"}}, {"refId": "B", "relativeTimeRange": {"from": 10800, "to": 0}, "datasourceUid": "__expr__", "model": {"conditions": [{"evaluator": {"params": [], "type": "gt"}, "operator": {"type": "and"}, "query": {"params": ["B"]}, "reducer": {"params": [], "type": "last"}, "type": "query"}], "datasource": {"type": "__expr__", "uid": "__expr__"}, "expression": "A", "intervalMs": 1000, "maxDataPoints": 43200, "reducer": "last", "refId": "B", "type": "reduce"}}, {"refId": "C", "relativeTimeRange": {"from": 10800, "to": 0}, "datasourceUid": "__expr__", "model": {"conditions": [{"evaluator": {"params": [0.95], "type": "gt"}, "operator": {"type": "and"}, "query": {"params": ["C"]}, "reducer": {"params": [], "type": "last"}, "type": "query", "unloadEvaluator": {"params": [0.9], "type": "lt"}}], "datasource": {"type": "__expr__", "uid": "__expr__"}, "expression": "B", "intervalMs": 1000, "maxDataPoints": 43200, "refId": "C", "type": "threshold"}}], "dashboardUid": "000000014", "panelId": 5, "noDataState": "NoData", "execErrState": "Error", "for": "1m", "annotations": {"__dashboardUid__": "000000014", "__panelId__": "5", "summary": "Memory usage for [ {{ index $labels \"hostname\" }} ] -> [ {{ index $labels \"instance\" }} ] has exceeded 95% for the last 1 minutes: {{ humanizePercentage $values.B.Value }}\n=============================="}, "labels": {"mon_type": "node"}, "isPaused": false}, {"uid": "dadea442-3a7a-4fcb-95fe-cee6569533f9", "title": "Linux Nodes Disk Usage", "condition": "C", "data": [{"refId": "A", "relativeTimeRange": {"from": 10800, "to": 0}, "datasourceUid": "d31c6998-358e-44d3-8542-ba13b1d024a5", "model": {"datasource": {"type": "prometheus", "uid": "d31c6998-358e-44d3-8542-ba13b1d024a5"}, "editorMode": "code", "expr": "(100.0 - 100 * ((node_filesystem_avail_bytes{mountpoint=\"/\", hostname=~\"(.+)\"} / 1000 / 1000 ) / (node_filesystem_size_bytes{mountpoint=\"/\", hostname=~\"(.+)\"} / 1024 / 1024))) / 100", "format": "time_series", "instant": false, "interval": "", "intervalFactor": 2, "intervalMs": 15000, "legendFormat": "{{hostname}} | {{instance}}: (mountpoint = {{mountpoint}})", "maxDataPoints": 43200, "range": true, "refId": "A"}}, {"refId": "B", "relativeTimeRange": {"from": 10800, "to": 0}, "datasourceUid": "__expr__", "model": {"conditions": [{"evaluator": {"params": [], "type": "gt"}, "operator": {"type": "and"}, "query": {"params": ["B"]}, "reducer": {"params": [], "type": "last"}, "type": "query"}], "datasource": {"type": "__expr__", "uid": "__expr__"}, "expression": "A", "intervalMs": 1000, "maxDataPoints": 43200, "reducer": "last", "refId": "B", "type": "reduce"}}, {"refId": "C", "relativeTimeRange": {"from": 10800, "to": 0}, "datasourceUid": "__expr__", "model": {"conditions": [{"evaluator": {"params": [0.9], "type": "gt"}, "operator": {"type": "and"}, "query": {"params": ["C"]}, "reducer": {"params": [], "type": "last"}, "type": "query", "unloadEvaluator": {"params": [0.9], "type": "lt"}}], "datasource": {"type": "__expr__", "uid": "__expr__"}, "expression": "B", "intervalMs": 1000, "maxDataPoints": 43200, "refId": "C", "type": "threshold"}}], "dashboardUid": "000000014", "panelId": 1, "noDataState": "NoData", "execErrState": "Error", "for": "1m", "annotations": {"__dashboardUid__": "000000014", "__panelId__": "1", "summary": "Disk usage for [ {{ index $labels \"hostname\" }} ] -> [ {{ index $labels \"instance\" }} ] has exceeded 90% for the last 1 minutes: {{ humanizePercentage $values.B.Value }}\n=============================="}, "labels": {"mon_type": "node"}, "isPaused": false}, {"uid": "db237980-a2e9-4579-8c4d-2049a4e88870", "title": "Linux Nodes CPU Usage", "condition": "C", "data": [{"refId": "A", "relativeTimeRange": {"from": 10800, "to": 0}, "datasourceUid": "d31c6998-358e-44d3-8542-ba13b1d024a5", "model": {"datasource": {"type": "prometheus", "uid": "d31c6998-358e-44d3-8542-ba13b1d024a5"}, "editorMode": "code", "expr": "1 - (avg by (hostname, instance) (irate(node_cpu_seconds_total{mode=\"idle\", hostname=~\"(.+)\"}[1m])))", "format": "time_series", "instant": false, "interval": "", "intervalFactor": 1, "intervalMs": 15000, "legendFormat": "{{hostname}} | {{instance}}", "maxDataPoints": 43200, "range": true, "refId": "A"}}, {"refId": "B", "relativeTimeRange": {"from": 10800, "to": 0}, "datasourceUid": "__expr__", "model": {"conditions": [{"evaluator": {"params": [], "type": "gt"}, "operator": {"type": "and"}, "query": {"params": ["B"]}, "reducer": {"params": [], "type": "last"}, "type": "query"}], "datasource": {"type": "__expr__", "uid": "__expr__"}, "expression": "A", "intervalMs": 1000, "maxDataPoints": 43200, "reducer": "last", "refId": "B", "type": "reduce"}}, {"refId": "C", "relativeTimeRange": {"from": 10800, "to": 0}, "datasourceUid": "__expr__", "model": {"conditions": [{"evaluator": {"params": [0.95], "type": "gt"}, "operator": {"type": "and"}, "query": {"params": ["C"]}, "reducer": {"params": [], "type": "last"}, "type": "query", "unloadEvaluator": {"params": [0.9], "type": "lt"}}], "datasource": {"type": "__expr__", "uid": "__expr__"}, "expression": "B", "intervalMs": 1000, "maxDataPoints": 43200, "refId": "C", "type": "threshold"}}], "dashboardUid": "000000014", "panelId": 6, "noDataState": "NoData", "execErrState": "Error", "for": "1m", "annotations": {"__dashboardUid__": "000000014", "__panelId__": "6", "summary": "CPU usage for [ {{ index $labels \"hostname\" }} ] -> [ {{ index $labels \"instance\" }} ] has exceeded 95% for the last 1 minutes: {{ humanizePercentage $values.B.Value }}\n=============================="}, "labels": {"mon_type": "node"}, "isPaused": false}, {"uid": "a8513c09-a18c-4090-8ee6-c3c2327623cc", "title": "Linux Nodes Sys Load (15m)", "condition": "C", "data": [{"refId": "A", "relativeTimeRange": {"from": 10800, "to": 0}, "datasourceUid": "d31c6998-358e-44d3-8542-ba13b1d024a5", "model": {"datasource": {"type": "prometheus", "uid": "d31c6998-358e-44d3-8542-ba13b1d024a5"}, "editorMode": "code", "exemplar": false, "expr": "node_load15{hostname=~\"(.+)\"}", "instant": false, "interval": "", "intervalMs": 15000, "legendFormat": "{{hostname}} | {{instance}}", "maxDataPoints": 43200, "range": true, "refId": "A"}}, {"refId": "B", "relativeTimeRange": {"from": 10800, "to": 0}, "datasourceUid": "__expr__", "model": {"conditions": [{"evaluator": {"params": [], "type": "gt"}, "operator": {"type": "and"}, "query": {"params": ["B"]}, "reducer": {"params": [], "type": "last"}, "type": "query"}], "datasource": {"type": "__expr__", "uid": "__expr__"}, "expression": "A", "intervalMs": 1000, "maxDataPoints": 43200, "reducer": "last", "refId": "B", "type": "reduce"}}, {"refId": "C", "relativeTimeRange": {"from": 10800, "to": 0}, "datasourceUid": "__expr__", "model": {"conditions": [{"evaluator": {"params": [15], "type": "gt"}, "operator": {"type": "and"}, "query": {"params": ["C"]}, "reducer": {"params": [], "type": "last"}, "type": "query", "unloadEvaluator": {"params": [12], "type": "lt"}}], "datasource": {"type": "__expr__", "uid": "__expr__"}, "expression": "B", "intervalMs": 1000, "maxDataPoints": 43200, "refId": "C", "type": "threshold"}}], "dashboardUid": "000000014", "panelId": 8, "noDataState": "NoData", "execErrState": "Error", "for": "1m", "annotations": {"__dashboardUid__": "000000014", "__panelId__": "8", "summary": "Linux Nodes Sys Load (15m) for [ {{ index $labels \"hostname\" }} ] -> [ {{ index $labels \"instance\" }} ] has exceeded 15 for the last 1 minutes: {{ humanize $values.B.Value }}\n=============================="}, "labels": {"mon_type": "node"}, "isPaused": false}, {"uid": "ee804194-1390-4d72-a437-c05992a1dd44", "title": "Inodes Usage", "condition": "C", "data": [{"refId": "A", "relativeTimeRange": {"from": 10800, "to": 0}, "datasourceUid": "d31c6998-358e-44d3-8542-ba13b1d024a5", "model": {"datasource": {"type": "prometheus", "uid": "d31c6998-358e-44d3-8542-ba13b1d024a5"}, "editorMode": "code", "expr": "(node_filesystem_files{mountpoint=\"/\", hostname=~\"(.+)\",device!~'rootfs'} - node_filesystem_files_free{mountpoint=\"/\", hostname=~\"(.+)\",device!~'rootfs'}) / node_filesystem_files{mountpoint=\"/\", hostname=~\"(.+)\",device!~'rootfs'}", "instant": false, "interval": "", "intervalMs": 15000, "legendFormat": "{{hostname}} | {{instance}}: (mountpoint = {{mountpoint}})", "maxDataPoints": 43200, "range": true, "refId": "A"}}, {"refId": "B", "relativeTimeRange": {"from": 10800, "to": 0}, "datasourceUid": "__expr__", "model": {"conditions": [{"evaluator": {"params": [], "type": "gt"}, "operator": {"type": "and"}, "query": {"params": ["A"]}, "reducer": {"params": [], "type": "last"}, "type": "query"}], "datasource": {"type": "__expr__", "uid": "__expr__"}, "expression": "A", "intervalMs": 1000, "maxDataPoints": 43200, "reducer": "last", "refId": "B", "type": "reduce"}}, {"refId": "C", "relativeTimeRange": {"from": 10800, "to": 0}, "datasourceUid": "__expr__", "model": {"conditions": [{"evaluator": {"params": [0.8], "type": "gt"}, "operator": {"type": "and"}, "query": {"params": ["C"]}, "reducer": {"params": [], "type": "last"}, "type": "query", "unloadEvaluator": {"params": [0.8], "type": "lt"}}], "datasource": {"type": "__expr__", "uid": "__expr__"}, "expression": "B", "intervalMs": 1000, "maxDataPoints": 43200, "refId": "C", "type": "threshold"}}], "dashboardUid": "000000014", "panelId": 17, "noDataState": "NoData", "execErrState": "Error", "for": "5m", "annotations": {"__dashboardUid__": "000000014", "__panelId__": "17", "summary": "Inodes usage for [ {{ index $labels \"hostname\" }} ] -> [ {{ index $labels \"instance\" }} ] has exceeded 80% for the last 5 minutes: {{ humanizePercentage $values.B.Value }}\n=============================="}, "labels": {"mon_type": "node"}, "isPaused": false}]}, {"orgId": 1, "name": "redis", "folder": "Alerting", "interval": "20s", "rules": [{"uid": "ec7ae537-9cba-4658-8151-9ebf1f5d8a49", "title": "Redis Resource Status", "condition": "C", "data": [{"refId": "A", "relativeTimeRange": {"from": 1800, "to": 0}, "datasourceUid": "d31c6998-358e-44d3-8542-ba13b1d024a5", "model": {"datasource": {"type": "prometheus", "uid": "d31c6998-358e-44d3-8542-ba13b1d024a5"}, "editorMode": "code", "exemplar": false, "expr": "1 - (avg by (hostname, instance) (irate(node_cpu_seconds_total{mode=\"idle\", projectname=~\"ya<PERSON>u\", hostname=~\"(p-redis.+)\"}[1m])))", "format": "time_series", "instant": true, "interval": "", "intervalFactor": 1, "intervalMs": 15000, "legendFormat": "{{hostname}} | {{instance}}", "maxDataPoints": 43200, "range": false, "refId": "A"}}, {"refId": "B", "relativeTimeRange": {"from": 600, "to": 0}, "datasourceUid": "d31c6998-358e-44d3-8542-ba13b1d024a5", "model": {"datasource": {"type": "prometheus", "uid": "d31c6998-358e-44d3-8542-ba13b1d024a5"}, "editorMode": "code", "expr": "(100 * (node_memory_MemTotal_bytes{projectname=~\"yakumaru\", hostname=~\"(p-redis.+)\"} - node_memory_MemFree_bytes{projectname=~\"yakumaru\", hostname=~\"(p-redis.+)\"} - node_memory_Buffers_bytes{projectname=~\"yakumaru\", hostname=~\"(p-redis.+)\"} - node_memory_Cached_bytes{projectname=~\"yakumar<PERSON>\", hostname=~\"(p-redis.+)\"}) / node_memory_MemTotal_bytes{projectname=~\"yakumaru\", hostname=~\"(p-redis.+)\"}) / 100", "instant": true, "intervalMs": 1000, "legendFormat": "__auto", "maxDataPoints": 43200, "range": false, "refId": "B"}}, {"refId": "C", "relativeTimeRange": {"from": 600, "to": 0}, "datasourceUid": "__expr__", "model": {"conditions": [{"evaluator": {"params": [0, 0], "type": "gt"}, "operator": {"type": "and"}, "query": {"params": []}, "reducer": {"params": [], "type": "avg"}, "type": "query"}], "datasource": {"name": "Expression", "type": "__expr__", "uid": "__expr__"}, "expression": "($A > 0.85) || ($B > 0.9)", "intervalMs": 1000, "maxDataPoints": 43200, "refId": "C", "type": "math"}}, {"refId": "D", "relativeTimeRange": {"from": 600, "to": 0}, "datasourceUid": "__expr__", "model": {"conditions": [{"evaluator": {"params": [0, 0], "type": "gt"}, "operator": {"type": "and"}, "query": {"params": []}, "reducer": {"params": [], "type": "avg"}, "type": "query"}], "datasource": {"name": "Expression", "type": "__expr__", "uid": "__expr__"}, "expression": "C", "intervalMs": 1000, "maxDataPoints": 43200, "refId": "D", "type": "threshold"}}], "dashboardUid": "f6e0410a-2940-4556-a35b-e81ba4e7ece2", "panelId": 14, "noDataState": "NoData", "execErrState": "Error", "for": "20s", "annotations": {"__dashboardUid__": "f6e0410a-2940-4556-a35b-e81ba4e7ece2", "__panelId__": "14", "summary": "1. Redis -> CPU：\n    [ CPU (Threshold: 85%) ] -> 在过去20秒的值为：[ {{ humanizePercentage $values.A.Value }} ]\n----------\n2. Redis -> MEM：\n    [ MEM (Threshold: 90%) ] -> 在过去20秒的值为：[ {{ humanizePercentage $values.B.Value }} ]\n=============================="}, "labels": {"mon_type": "redis"}, "isPaused": false}]}, {"orgId": 1, "name": "status", "folder": "Alerting", "interval": "30s", "rules": [{"uid": "cfa86023-8c65-4dd4-b889-3a8bd0fb6f9b", "title": "Scheduling Status", "condition": "G", "data": [{"refId": "A", "relativeTimeRange": {"from": 1800, "to": 0}, "datasourceUid": "d31c6998-358e-44d3-8542-ba13b1d024a5", "model": {"datasource": {"type": "prometheus", "uid": "d31c6998-358e-44d3-8542-ba13b1d024a5"}, "disableTextWrap": false, "editorMode": "code", "expr": "kenta_scheduling_status{status=\"queued\"}", "format": "time_series", "fullMetaSearch": false, "includeNullMetadata": true, "instant": false, "intervalMs": 1000, "legendFormat": "__auto", "maxDataPoints": 43200, "range": true, "refId": "A", "useBackend": false}}, {"refId": "B", "relativeTimeRange": {"from": 600, "to": 0}, "datasourceUid": "d31c6998-358e-44d3-8542-ba13b1d024a5", "model": {"datasource": {"type": "prometheus", "uid": "d31c6998-358e-44d3-8542-ba13b1d024a5"}, "disableTextWrap": false, "editorMode": "code", "expr": "kenta_scheduling_status{status=\"pt\"}", "format": "time_series", "fullMetaSearch": false, "includeNullMetadata": true, "instant": false, "intervalMs": 1000, "legendFormat": "__auto", "maxDataPoints": 43200, "range": true, "refId": "B", "useBackend": false}}, {"refId": "C", "relativeTimeRange": {"from": 600, "to": 0}, "datasourceUid": "d31c6998-358e-44d3-8542-ba13b1d024a5", "model": {"datasource": {"type": "prometheus", "uid": "d31c6998-358e-44d3-8542-ba13b1d024a5"}, "editorMode": "code", "exemplar": false, "expr": "kenta_scheduling_status{status=\"merge\"}", "format": "time_series", "instant": false, "intervalMs": 1000, "legendFormat": "__auto", "maxDataPoints": 43200, "range": true, "refId": "C"}}, {"refId": "G", "relativeTimeRange": {"from": 600, "to": 0}, "datasourceUid": "__expr__", "model": {"conditions": [{"evaluator": {"params": [0, 0], "type": "gt"}, "operator": {"type": "and"}, "query": {"params": []}, "reducer": {"params": [], "type": "avg"}, "type": "query"}], "datasource": {"name": "Expression", "type": "__expr__", "uid": "__expr__"}, "expression": "(($D > 60.0) && ($D > $E)) || ($F > 100.0)", "intervalMs": 1000, "maxDataPoints": 43200, "refId": "G", "type": "math"}}, {"refId": "H", "relativeTimeRange": {"from": 600, "to": 0}, "datasourceUid": "__expr__", "model": {"conditions": [{"evaluator": {"params": [0, 0], "type": "gt"}, "operator": {"type": "and"}, "query": {"params": []}, "reducer": {"params": [], "type": "avg"}, "type": "query"}], "datasource": {"name": "Expression", "type": "__expr__", "uid": "__expr__"}, "expression": "G", "intervalMs": 1000, "maxDataPoints": 43200, "refId": "H", "type": "threshold"}}, {"refId": "D", "relativeTimeRange": {"from": 1800, "to": 0}, "datasourceUid": "__expr__", "model": {"conditions": [{"evaluator": {"params": [0, 0], "type": "gt"}, "operator": {"type": "and"}, "query": {"params": []}, "reducer": {"params": [], "type": "avg"}, "type": "query"}], "datasource": {"name": "Expression", "type": "__expr__", "uid": "__expr__"}, "expression": "A", "intervalMs": 1000, "maxDataPoints": 43200, "reducer": "last", "refId": "D", "type": "reduce"}}, {"refId": "E", "relativeTimeRange": {"from": 600, "to": 0}, "datasourceUid": "__expr__", "model": {"conditions": [{"evaluator": {"params": [0, 0], "type": "gt"}, "operator": {"type": "and"}, "query": {"params": []}, "reducer": {"params": [], "type": "avg"}, "type": "query"}], "datasource": {"name": "Expression", "type": "__expr__", "uid": "__expr__"}, "expression": "B", "intervalMs": 1000, "maxDataPoints": 43200, "reducer": "last", "refId": "E", "type": "reduce"}}, {"refId": "F", "relativeTimeRange": {"from": 600, "to": 0}, "datasourceUid": "__expr__", "model": {"conditions": [{"evaluator": {"params": [0, 0], "type": "gt"}, "operator": {"type": "and"}, "query": {"params": []}, "reducer": {"params": [], "type": "avg"}, "type": "query"}], "datasource": {"name": "Expression", "type": "__expr__", "uid": "__expr__"}, "expression": "C", "intervalMs": 1000, "maxDataPoints": 43200, "reducer": "last", "refId": "F", "type": "reduce"}}], "dashboardUid": "f6e0410a-2940-4556-a35b-e81ba4e7ece2", "panelId": 2, "noDataState": "NoData", "execErrState": "Error", "for": "30s", "annotations": {"__dashboardUid__": "f6e0410a-2940-4556-a35b-e81ba4e7ece2", "__panelId__": "2", "summary": "1. 调度状态：\n    [ Queued > PT ]\n    [ Queued ] -> 在过去30秒的值为：[ {{ $values.D.Value }} ]\n----------\nOR\n----------\n2. 调度状态：\n    [ Merge ] -> 在过去30秒的值为：[ {{ $values.F.Value }} ]\n=============================="}, "labels": {"mon_type": "status"}, "isPaused": false}, {"uid": "c48d7a75-0613-46a4-8a41-ed892bc1f4eb", "title": "MongoDB Disk Read IOPS", "condition": "B", "data": [{"refId": "A", "relativeTimeRange": {"from": 3600, "to": 0}, "datasourceUid": "d31c6998-358e-44d3-8542-ba13b1d024a5", "model": {"datasource": {"type": "prometheus", "uid": "d31c6998-358e-44d3-8542-ba13b1d024a5"}, "editorMode": "code", "exemplar": true, "expr": "label_replace( sum(rate(hardware_disk_metrics_read_count{group_id=~\"(.+)\", cl_name=~\"aws-bureau\", instance=~\"(.+)\"}[30s])) by (instance, disk_name) , \"hostname\", \"$1\", \"instance\", \"(.*)\")", "instant": true, "interval": "", "intervalMs": 15000, "legendFormat": "disk - {{disk_name}} host - {{hostname}} ", "maxDataPoints": 43200, "range": false, "refId": "A"}}, {"refId": "B", "relativeTimeRange": {"from": 3600, "to": 0}, "datasourceUid": "__expr__", "model": {"conditions": [{"evaluator": {"params": [2500], "type": "gt"}, "operator": {"type": "and"}, "query": {"params": ["C"]}, "reducer": {"params": [], "type": "last"}, "type": "query"}], "datasource": {"type": "__expr__", "uid": "__expr__"}, "expression": "A", "intervalMs": 1000, "maxDataPoints": 43200, "refId": "B", "type": "threshold"}}], "dashboardUid": "KsE2_DWSz", "panelId": 76, "noDataState": "NoData", "execErrState": "Error", "for": "1m", "annotations": {"__dashboardUid__": "KsE2_DWSz", "__panelId__": "76", "summary": "MongoDB Instance: [ {{ index $labels \"instance\" }} ]\nMongoDB Disk Read IOPS: [ {{ humanize $values.A.Value }} ]"}, "labels": {"mon_type": "mongo"}, "isPaused": false}]}]}