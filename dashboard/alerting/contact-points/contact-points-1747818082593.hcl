resource "grafana_contact_point" "contact_point_0" {
  name = "NeoX-MongoDB"

  wecom {
    url      = "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=3da4a5b7-16e4-4ff6-8139-ed194e19b379"
    message  = "{{ template \"mongo.message\" . }}"
    title    = "{{ template \"mongo.title\" . }}"
    msg_type = "text"
  }
}
resource "grafana_contact_point" "contact_point_1" {
  name = "NeoX-Node"

  wecom {
    url      = "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=507191c2-433c-4f79-b308-92b2a51c41a7"
    message  = "{{ template \"slack.message\" . }}"
    msg_type = "text"
  }
}
resource "grafana_contact_point" "contact_point_2" {
  name = "NeoX-Redis"

  wecom {
    url      = "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=3da4a5b7-16e4-4ff6-8139-ed194e19b379"
    message  = "{{ template \"redis.message\" . }}"
    title    = "{{ template \"redis.title\" . }}"
    msg_type = "text"
  }
}
resource "grafana_contact_point" "contact_point_3" {
  name = "NeoX-Status"

  wecom {
    url      = "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=3da4a5b7-16e4-4ff6-8139-ed194e19b379"
    message  = "{{ template \"status.message\" . }}"
    title    = "{{ template \"status.title\" . }}"
    msg_type = "text"
    to_user  = "@all"
  }
}
