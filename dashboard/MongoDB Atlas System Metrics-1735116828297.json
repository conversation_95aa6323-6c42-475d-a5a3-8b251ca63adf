{"annotations": {"list": [{"builtIn": 1, "datasource": {"type": "datasource", "uid": "grafana"}, "enable": true, "hide": true, "iconColor": "rgba(0, 211, 255, 1)", "name": "Annotations & Alerts", "target": {"limit": 100, "matchAny": false, "tags": [], "type": "dashboard"}, "type": "dashboard"}]}, "description": "MongoDB Atlas System Metrics collected with official prometheus integration.", "editable": true, "fiscalYearStartMonth": 0, "gnetId": 19654, "graphTooltip": 0, "id": 29, "links": [], "liveNow": false, "panels": [{"collapsed": false, "datasource": {"type": "prometheus", "uid": "d31c6998-358e-44d3-8542-ba13b1d024a5"}, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 0}, "id": 10, "panels": [], "targets": [{"datasource": {"type": "prometheus", "uid": "d31c6998-358e-44d3-8542-ba13b1d024a5"}, "refId": "A"}], "title": "Overview", "type": "row"}, {"datasource": {"type": "prometheus", "uid": "d31c6998-358e-44d3-8542-ba13b1d024a5"}, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "custom": {"align": "auto", "cellOptions": {"type": "auto"}, "inspect": false}, "decimals": 2, "displayName": "", "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "short", "unitScale": true}, "overrides": [{"matcher": {"id": "by<PERSON><PERSON>", "options": "Value"}, "properties": [{"id": "unit", "value": "short"}, {"id": "decimals", "value": 2}, {"id": "custom.align"}]}]}, "gridPos": {"h": 4, "w": 24, "x": 0, "y": 1}, "id": 41, "options": {"cellHeight": "sm", "footer": {"countRows": false, "fields": "", "reducer": ["sum"], "show": false}, "showHeader": true}, "pluginVersion": "10.3.3", "targets": [{"datasource": {"type": "prometheus", "uid": "d31c6998-358e-44d3-8542-ba13b1d024a5"}, "exemplar": false, "expr": "label_replace( sum(mongodb_up{group_id=~\"$group_id\", cl_name=~\"$cl_name\", instance=~\"$host.*\"}) by ( group_id, org_id, rs_nm, cl_name), \"hostname\", \"$1\", \"instance\", \"(.*)\")", "format": "table", "hide": false, "instant": true, "interval": "", "legendFormat": "", "refId": "A"}], "title": "Group Metadata", "transformations": [{"id": "organize", "options": {"excludeByName": {"Time": true, "Value #A": true, "instance": true, "rs_nm": false}, "indexByName": {"": 2, "Time": 0, "Value #A": 6, "cl_name": 4, "group_id": 3, "org_id": 1, "rs_nm": 5}, "renameByName": {"": "Group Name ", "Time": "", "cl_name": "Cluster Name", "group_id": "Group Id", "hostname": "Host", "instance": "", "org_id": "Org Id", "process_port": "Port", "replica_state": "ReplicaSet State", "rs_nm": "ReplicaSet Name"}}}, {"id": "groupBy", "options": {"fields": {"Cluster Name": {"aggregations": [], "operation": "groupby"}, "Group Id": {"aggregations": [], "operation": "groupby"}, "Group Name ": {"aggregations": [], "operation": "groupby"}, "Host": {"aggregations": [], "operation": "groupby"}, "Host ": {"aggregations": [], "operation": "groupby"}, "Org Id": {"aggregations": [], "operation": "groupby"}, "Port": {"aggregations": [], "operation": "groupby"}, "Replica set state": {"aggregations": [], "operation": "groupby"}, "ReplicaSet Name": {"aggregations": [], "operation": "groupby"}, "ReplicaSet State": {"aggregations": [], "operation": "groupby"}, "host ": {"aggregations": [], "operation": "groupby"}, "hostname": {"aggregations": [], "operation": "groupby"}, "instance": {"aggregations": [], "operation": "groupby"}, "port": {"aggregations": [], "operation": "groupby"}, "process_port": {"aggregations": [], "operation": "groupby"}, "replica set": {"aggregations": [], "operation": "groupby"}, "replica set state": {"aggregations": [], "operation": "groupby"}, "replica_state": {"aggregations": [], "operation": "groupby"}, "rs_nm": {"aggregations": [], "operation": "groupby"}}}}, {"id": "merge", "options": {"reducers": []}}], "type": "table"}, {"datasource": {"type": "prometheus", "uid": "d31c6998-358e-44d3-8542-ba13b1d024a5"}, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "custom": {"align": "auto", "cellOptions": {"type": "auto"}, "inspect": false}, "decimals": 0, "displayName": "", "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "none", "unitScale": true}, "overrides": []}, "gridPos": {"h": 6, "w": 24, "x": 0, "y": 5}, "id": 42, "options": {"cellHeight": "sm", "footer": {"countRows": false, "fields": "", "reducer": ["sum"], "show": false}, "showHeader": true}, "pluginVersion": "10.3.3", "targets": [{"datasource": {"type": "prometheus", "uid": "d31c6998-358e-44d3-8542-ba13b1d024a5"}, "exemplar": false, "expr": "label_replace( sum(mongodb_info{group_id=~\"$group_id\", cl_name=~\"$cl_name\", instance=~\"$host.*\"}) by (instance, replica_state_name, process_port, rs_nm, process_type), \"hostname\", \"$1\", \"instance\", \"(.*):.*\")", "format": "table", "hide": false, "instant": true, "interval": "", "legendFormat": "", "refId": "A"}], "title": "Cluster host list", "transformations": [{"id": "organize", "options": {"excludeByName": {"Time": true, "Value #A": true, "instance": false, "rs_nm": false}, "indexByName": {"Time": 0, "Value": 6, "instance": 1, "process_port": 2, "process_type": 4, "replica_state_name": 5, "rs_nm": 3}, "renameByName": {"Time": "", "Value": "", "hostname": "Host", "instance": "Hostname", "process_port": "Port", "process_type": "Process Type", "replica_state": "ReplicaSet State", "replica_state_name": "Replica State", "rs_nm": "ReplicaSet Name"}}}, {"id": "groupBy", "options": {"fields": {"Host": {"aggregations": [], "operation": "groupby"}, "Host ": {"aggregations": [], "operation": "groupby"}, "Hostname": {"aggregations": [], "operation": "groupby"}, "Port": {"aggregations": [], "operation": "groupby"}, "Process Type": {"aggregations": [], "operation": "groupby"}, "Replica State": {"aggregations": [], "operation": "groupby"}, "Replica set state": {"aggregations": [], "operation": "groupby"}, "ReplicaSet Name": {"aggregations": [], "operation": "groupby"}, "ReplicaSet State": {"aggregations": [], "operation": "groupby"}, "host ": {"aggregations": [], "operation": "groupby"}, "hostname": {"aggregations": [], "operation": "groupby"}, "instance": {"aggregations": [], "operation": "groupby"}, "port": {"aggregations": [], "operation": "groupby"}, "process_port": {"aggregations": [], "operation": "groupby"}, "replica set": {"aggregations": [], "operation": "groupby"}, "replica set state": {"aggregations": [], "operation": "groupby"}, "replica_state": {"aggregations": [], "operation": "groupby"}, "rs_nm": {"aggregations": [], "operation": "groupby"}}}}, {"id": "merge", "options": {"reducers": []}}], "type": "table"}, {"collapsed": false, "datasource": {"type": "prometheus", "uid": "d31c6998-358e-44d3-8542-ba13b1d024a5"}, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 11}, "id": 28, "panels": [], "targets": [{"datasource": {"type": "prometheus", "uid": "d31c6998-358e-44d3-8542-ba13b1d024a5"}, "refId": "A"}], "title": "Connections", "type": "row"}, {"datasource": {"uid": "d31c6998-358e-44d3-8542-ba13b1d024a5"}, "description": "The number of currently active connections to this server. A stack is allocated per connection; thus very many connections can result in significant RAM usage.", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "links": [], "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "none", "unitScale": true}, "overrides": []}, "gridPos": {"h": 9, "w": 24, "x": 0, "y": 12}, "id": 81, "options": {"legend": {"calcs": ["min", "max", "mean", "lastNotNull"], "displayMode": "table", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "multi", "sort": "none"}}, "pluginVersion": "10.3.3", "targets": [{"datasource": {"uid": "d31c6998-358e-44d3-8542-ba13b1d024a5"}, "exemplar": true, "expr": "label_replace(sum(mongodb_connections_current{group_id=~\"$group_id\", cl_name=~\"$cl_name\", rs_nm=~\"$rs_nm\", instance=~\"$host.*\",  process_port=~\"$process_port\"}) by (instance, process_port) , \"hostname\", \"$1\", \"instance\", \"(.*)\")", "interval": "", "legendFormat": "{{hostname}}:{{process_port}}", "refId": "A"}], "title": "Connections - Current", "type": "timeseries"}, {"collapsed": false, "datasource": {"type": "prometheus", "uid": "d31c6998-358e-44d3-8542-ba13b1d024a5"}, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 21}, "id": 95, "panels": [], "targets": [{"datasource": {"type": "prometheus", "uid": "d31c6998-358e-44d3-8542-ba13b1d024a5"}, "refId": "A"}], "title": "Operation Execution Times", "type": "row"}, {"datasource": {"uid": "d31c6998-358e-44d3-8542-ba13b1d024a5"}, "description": "The average execution time in milliseconds per read operation over the selected sample period.", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "links": [], "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "µs", "unitScale": true}, "overrides": []}, "gridPos": {"h": 8, "w": 8, "x": 0, "y": 22}, "id": 102, "options": {"legend": {"calcs": ["min", "max", "mean", "lastNotNull"], "displayMode": "table", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "multi", "sort": "none"}}, "pluginVersion": "10.3.3", "targets": [{"datasource": {"uid": "d31c6998-358e-44d3-8542-ba13b1d024a5"}, "exemplar": true, "expr": "label_replace(sum(irate(mongodb_opLatencies_reads_latency{group_id=~\"$group_id\", cl_name=~\"$cl_name\", rs_nm=~\"$rs_nm\", instance=~\"$host.*\",  process_port=~\"$process_port\"}[$Interval])/irate(mongodb_opLatencies_reads_ops{group_id=~\"$group_id\", cl_name=~\"$cl_name\", rs_nm=~\"$rs_nm\", instance=~\"$host.*\",  process_port=~\"$process_port\"}[$Interval])) by (instance, process_port) , \"hostname\", \"$1\", \"instance\", \"(.*)\")", "interval": "", "legendFormat": "{{hostname}}:{{process_port}}", "refId": "A"}], "title": "Operation Execution Times - Avg Ms/Read", "type": "timeseries"}, {"datasource": {"uid": "d31c6998-358e-44d3-8542-ba13b1d024a5"}, "description": "The average execution time in milliseconds per write operation over the selected sample period.", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "links": [], "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "µs", "unitScale": true}, "overrides": []}, "gridPos": {"h": 8, "w": 8, "x": 8, "y": 22}, "id": 104, "options": {"legend": {"calcs": ["min", "max", "mean", "lastNotNull"], "displayMode": "table", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "multi", "sort": "none"}}, "pluginVersion": "10.3.3", "targets": [{"datasource": {"uid": "d31c6998-358e-44d3-8542-ba13b1d024a5"}, "exemplar": true, "expr": "label_replace(sum(irate(mongodb_opLatencies_writes_latency{group_id=~\"$group_id\", cl_name=~\"$cl_name\", rs_nm=~\"$rs_nm\", instance=~\"$host.*\",  process_port=~\"$process_port\"}[$Interval])/irate(mongodb_opLatencies_writes_ops{group_id=~\"$group_id\", cl_name=~\"$cl_name\", rs_nm=~\"$rs_nm\", instance=~\"$host.*\",  process_port=~\"$process_port\"}[$Interval])) by (instance, process_port) , \"hostname\", \"$1\", \"instance\", \"(.*)\")", "interval": "", "legendFormat": "{{hostname}}:{{process_port}}", "refId": "A"}], "title": "Operation Execution Times - Avg Ms/Write", "type": "timeseries"}, {"datasource": {"uid": "d31c6998-358e-44d3-8542-ba13b1d024a5"}, "description": "The average execution time in milliseconds per command operation over the selected sample period.", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "links": [], "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "µs", "unitScale": true}, "overrides": []}, "gridPos": {"h": 8, "w": 8, "x": 16, "y": 22}, "id": 103, "options": {"legend": {"calcs": ["min", "max", "mean", "lastNotNull"], "displayMode": "table", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "multi", "sort": "none"}}, "pluginVersion": "10.3.3", "targets": [{"datasource": {"uid": "d31c6998-358e-44d3-8542-ba13b1d024a5"}, "exemplar": true, "expr": "label_replace(sum(irate(mongodb_opLatencies_commands_latency{group_id=~\"$group_id\", cl_name=~\"$cl_name\", rs_nm=~\"$rs_nm\", instance=~\"$host.*\",  process_port=~\"$process_port\"}[$Interval])/irate(mongodb_opLatencies_commands_ops{group_id=~\"$group_id\", cl_name=~\"$cl_name\", rs_nm=~\"$rs_nm\", instance=~\"$host.*\",  process_port=~\"$process_port\"}[$Interval])) by (instance, process_port) , \"hostname\", \"$1\", \"instance\", \"(.*)\")", "interval": "", "legendFormat": "{{hostname}}:{{process_port}}", "refId": "A"}], "title": "Operation Execution Times - Avg Ms/Command", "type": "timeseries"}, {"collapsed": true, "datasource": {"type": "prometheus", "uid": "d31c6998-358e-44d3-8542-ba13b1d024a5"}, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 30}, "id": 93, "panels": [{"datasource": {"uid": "d31c6998-358e-44d3-8542-ba13b1d024a5"}, "description": "The average rate of commands performed per second over the selected sample period", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "links": [], "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "/s", "unitScale": true}, "overrides": []}, "gridPos": {"h": 8, "w": 8, "x": 0, "y": 31}, "id": 96, "options": {"legend": {"calcs": ["max", "mean", "lastNotNull"], "displayMode": "table", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "multi", "sort": "none"}}, "pluginVersion": "10.3.3", "targets": [{"datasource": {"uid": "d31c6998-358e-44d3-8542-ba13b1d024a5"}, "exemplar": true, "expr": "label_replace(sum(irate(mongodb_opcounters_command{group_id=~\"$group_id\", cl_name=~\"$cl_name\", rs_nm=~\"$rs_nm\", instance=~\"$host.*\",  process_port=~\"$process_port\"}[$Interval])) by (instance, process_port) , \"hostname\", \"$1\", \"instance\", \"(.*)\")", "interval": "", "legendFormat": "{{hostname}}:{{process_port}}", "refId": "A"}], "title": "Opcounters - Command", "type": "timeseries"}, {"datasource": {"uid": "d31c6998-358e-44d3-8542-ba13b1d024a5"}, "description": "The average rate of queries performed per second over the selected sample period", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "links": [], "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "/s", "unitScale": true}, "overrides": []}, "gridPos": {"h": 8, "w": 8, "x": 8, "y": 31}, "id": 101, "options": {"legend": {"calcs": ["max", "min", "lastNotNull"], "displayMode": "table", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "multi", "sort": "none"}}, "pluginVersion": "10.3.3", "targets": [{"datasource": {"uid": "d31c6998-358e-44d3-8542-ba13b1d024a5"}, "exemplar": true, "expr": "label_replace(sum(irate(mongodb_opcounters_query{group_id=~\"$group_id\", cl_name=~\"$cl_name\", rs_nm=~\"$rs_nm\", instance=~\"$host.*\",  process_port=~\"$process_port\"}[$Interval])) by (instance, process_port) , \"hostname\", \"$1\", \"instance\", \"(.*)\")", "interval": "", "legendFormat": "{{hostname}}:{{process_port}}", "refId": "A"}], "title": "Opcounters - Query", "type": "timeseries"}, {"datasource": {"uid": "d31c6998-358e-44d3-8542-ba13b1d024a5"}, "description": "The average rate of updates performed per second over the selected sample period", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "links": [], "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "/s", "unitScale": true}, "overrides": []}, "gridPos": {"h": 8, "w": 8, "x": 16, "y": 31}, "id": 100, "options": {"legend": {"calcs": ["max", "mean", "lastNotNull"], "displayMode": "table", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "multi", "sort": "none"}}, "pluginVersion": "10.3.3", "targets": [{"datasource": {"uid": "d31c6998-358e-44d3-8542-ba13b1d024a5"}, "exemplar": true, "expr": "label_replace(sum(irate(mongodb_opcounters_update{group_id=~\"$group_id\", cl_name=~\"$cl_name\", rs_nm=~\"$rs_nm\", instance=~\"$host.*\",  process_port=~\"$process_port\"}[$Interval])) by (instance, process_port) , \"hostname\", \"$1\", \"instance\", \"(.*)\")", "interval": "", "legendFormat": "{{hostname}}:{{process_port}}", "refId": "A"}], "title": "Opcounters - Update", "type": "timeseries"}, {"datasource": {"uid": "d31c6998-358e-44d3-8542-ba13b1d024a5"}, "description": "The average rate of deletes performed per second over the selected sample period", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "links": [], "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "/s", "unitScale": true}, "overrides": []}, "gridPos": {"h": 8, "w": 8, "x": 0, "y": 39}, "id": 99, "options": {"legend": {"calcs": ["max", "mean", "lastNotNull"], "displayMode": "table", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "multi", "sort": "none"}}, "pluginVersion": "10.3.3", "targets": [{"datasource": {"uid": "d31c6998-358e-44d3-8542-ba13b1d024a5"}, "exemplar": true, "expr": "label_replace(sum(irate(mongodb_opcounters_delete{group_id=~\"$group_id\", cl_name=~\"$cl_name\", rs_nm=~\"$rs_nm\", instance=~\"$host.*\",  process_port=~\"$process_port\"}[$Interval])) by (instance, process_port) , \"hostname\", \"$1\", \"instance\", \"(.*)\")", "interval": "", "legendFormat": "{{hostname}}:{{process_port}}", "refId": "A"}], "title": "Opcounters - Delete", "type": "timeseries"}, {"datasource": {"uid": "d31c6998-358e-44d3-8542-ba13b1d024a5"}, "description": "The average rate of getMores performed per second on any cursor over the selected sample period. On a primary, this number can be high even if the query count is low as the secondaries \"getMore\" from the primary often as part of replication.", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "links": [], "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "/s", "unitScale": true}, "overrides": []}, "gridPos": {"h": 8, "w": 8, "x": 8, "y": 39}, "id": 97, "options": {"legend": {"calcs": ["max", "mean", "lastNotNull"], "displayMode": "table", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "multi", "sort": "none"}}, "pluginVersion": "10.3.3", "targets": [{"datasource": {"uid": "d31c6998-358e-44d3-8542-ba13b1d024a5"}, "exemplar": true, "expr": "label_replace(sum(irate(mongodb_opcounters_getmore{group_id=~\"$group_id\", cl_name=~\"$cl_name\", rs_nm=~\"$rs_nm\", instance=~\"$host.*\",  process_port=~\"$process_port\"}[$Interval])) by (instance, process_port) , \"hostname\", \"$1\", \"instance\", \"(.*)\")", "interval": "", "legendFormat": "{{hostname}}:{{process_port}}", "refId": "A"}], "title": "Opcounters - Getmore", "type": "timeseries"}, {"datasource": {"uid": "d31c6998-358e-44d3-8542-ba13b1d024a5"}, "description": "The average rate of inserts performed per second over the selected sample period", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "links": [], "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "/s", "unitScale": true}, "overrides": []}, "gridPos": {"h": 8, "w": 8, "x": 16, "y": 39}, "id": 98, "options": {"legend": {"calcs": ["max", "mean", "lastNotNull"], "displayMode": "table", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "multi", "sort": "none"}}, "pluginVersion": "10.3.3", "targets": [{"datasource": {"uid": "d31c6998-358e-44d3-8542-ba13b1d024a5"}, "exemplar": true, "expr": "label_replace(sum(irate(mongodb_opcounters_insert{group_id=~\"$group_id\", cl_name=~\"$cl_name\", rs_nm=~\"$rs_nm\", instance=~\"$host.*\",  process_port=~\"$process_port\"}[$Interval])) by (instance, process_port) , \"hostname\", \"$1\", \"instance\", \"(.*)\")", "interval": "", "legendFormat": "{{hostname}}:{{process_port}}", "refId": "A"}], "title": "Opcounters - Insert", "type": "timeseries"}], "targets": [{"datasource": {"type": "prometheus", "uid": "d31c6998-358e-44d3-8542-ba13b1d024a5"}, "refId": "A"}], "title": "Opcounters", "type": "row"}, {"collapsed": false, "datasource": {"type": "prometheus", "uid": "d31c6998-358e-44d3-8542-ba13b1d024a5"}, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 31}, "id": 111, "panels": [], "targets": [{"datasource": {"type": "prometheus", "uid": "d31c6998-358e-44d3-8542-ba13b1d024a5"}, "refId": "A"}], "title": "Query Executor", "type": "row"}, {"datasource": {"uid": "d31c6998-358e-44d3-8542-ba13b1d024a5"}, "description": "The average rate per second over the selected sample period of index items scanned during queries and query-plan evaluation. This rate is driven by the same value as totalKeysExamined in the output of explain().", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "links": [], "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "/s", "unitScale": true}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 32}, "id": 108, "options": {"legend": {"calcs": ["min", "max", "lastNotNull"], "displayMode": "table", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "multi", "sort": "none"}}, "pluginVersion": "10.3.3", "targets": [{"datasource": {"uid": "d31c6998-358e-44d3-8542-ba13b1d024a5"}, "exemplar": true, "expr": "label_replace(sum(irate(mongodb_metrics_queryExecutor_scanned{group_id=~\"$group_id\", cl_name=~\"$cl_name\", rs_nm=~\"$rs_nm\", instance=~\"$host.*\",  process_port=~\"$process_port\"}[$Interval])) by (instance, process_port) , \"hostname\", \"$1\", \"instance\", \"(.*)\")", "interval": "", "legendFormat": "{{hostname}}:{{process_port}}", "refId": "A"}], "title": "Query Executor - <PERSON><PERSON><PERSON>", "type": "timeseries"}, {"datasource": {"uid": "d31c6998-358e-44d3-8542-ba13b1d024a5"}, "description": "The average rate per second over the selected sample period of documents scanned during queries and query-plan evaluation. This rate is driven by the same value as totalDocsExamined in the output of explain().", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "links": [], "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "/s", "unitScale": true}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 32}, "id": 109, "options": {"legend": {"calcs": ["min", "max", "lastNotNull"], "displayMode": "table", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "multi", "sort": "none"}}, "pluginVersion": "10.3.3", "targets": [{"datasource": {"uid": "d31c6998-358e-44d3-8542-ba13b1d024a5"}, "exemplar": true, "expr": "label_replace(sum(irate(mongodb_metrics_queryExecutor_scannedObjects{group_id=~\"$group_id\", cl_name=~\"$cl_name\", rs_nm=~\"$rs_nm\", instance=~\"$host.*\",  process_port=~\"$process_port\"}[$Interval])) by (instance, process_port) , \"hostname\", \"$1\", \"instance\", \"(.*)\")", "interval": "", "legendFormat": "{{hostname}}:{{process_port}}", "refId": "A"}], "title": "Query Executor - Scanned Objects", "type": "timeseries"}, {"collapsed": false, "datasource": {"type": "prometheus", "uid": "d31c6998-358e-44d3-8542-ba13b1d024a5"}, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 40}, "id": 8, "panels": [], "targets": [{"datasource": {"type": "prometheus", "uid": "d31c6998-358e-44d3-8542-ba13b1d024a5"}, "refId": "A"}], "title": "Memory", "type": "row"}, {"datasource": {"uid": "d31c6998-358e-44d3-8542-ba13b1d024a5"}, "description": "The number of megabytes resident. MMAPv1: It is typical over time, on a dedicated database server, for this number to approach the amount of physical ram on the box. WiredTiger: In a standard deployment resident is the amount of memory used by the WiredTiger cache plus the memory dedicated to other in memory structures used by the mongod process. By default, mongod with WiredTiger reserves 50% of the total physical memory on the server for the cache and at steady state, WiredTiger tries to limit cache usage to 80% of that total. For example, if a server has 16GB of memory, WiredTiger will assume it can use 8GB for cache and at steady state should use about 6.5GB.", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "links": [], "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "decmbytes", "unitScale": true}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 41}, "id": 2, "options": {"legend": {"calcs": ["min", "max", "lastNotNull"], "displayMode": "table", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "multi", "sort": "none"}}, "pluginVersion": "10.3.3", "targets": [{"datasource": {"uid": "d31c6998-358e-44d3-8542-ba13b1d024a5"}, "exemplar": true, "expr": "label_replace(sum(mongodb_mem_resident{group_id=~\"$group_id\", cl_name=~\"$cl_name\", rs_nm=~\"$rs_nm\", instance=~\"$host.*\",  process_port=~\"$process_port\"}) by (instance, process_port) , \"hostname\", \"$1\", \"instance\", \"(.*)\")", "interval": "", "legendFormat": "{{hostname}}:{{process_port}}", "refId": "A"}], "title": "Memory - Resident", "type": "timeseries"}, {"datasource": {"uid": "d31c6998-358e-44d3-8542-ba13b1d024a5"}, "description": "The virtual megabytes for the mongod process. MMAPv1: Generally virtual should be a little larger than mapped (or 2x with --journal), but if virtual is many gigabytes larger, it indicates that excessive memory is being used by other aspects than the memory mapping of files -- that would be bad/suboptimal. The most common case of usage of a high amount of memory for non-mapped is that there are very many connections to the database. Each connection has a thread stack and the memory for those stacks can add up to a considerable amount. WiredTiger: Generally virtual should be a little larger than mapped, but if virtual is many gigabytes larger, it indicates that excessive memory is being used by other aspects than the memory mapping of files -- that would be bad/suboptimal. The most common case of usage of a high amount of memory for non-mapped is that there are very many connections to the database. Each connection has a thread stack and the memory for those stacks can add up to a considerable amount.", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "links": [], "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "decmbytes", "unitScale": true}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 41}, "id": 72, "options": {"legend": {"calcs": ["min", "max", "lastNotNull"], "displayMode": "table", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "multi", "sort": "none"}}, "pluginVersion": "10.3.3", "targets": [{"datasource": {"uid": "d31c6998-358e-44d3-8542-ba13b1d024a5"}, "exemplar": true, "expr": "label_replace(sum(mongodb_mem_virtual{group_id=~\"$group_id\", cl_name=~\"$cl_name\", rs_nm=~\"$rs_nm\", instance=~\"$host.*\",  process_port=~\"$process_port\"}) by (instance, process_port) , \"hostname\", \"$1\", \"instance\", \"(.*)\")", "interval": "", "legendFormat": "{{hostname}}:{{process_port}}", "refId": "A"}], "title": "Memory - Virtual", "type": "timeseries"}, {"collapsed": false, "datasource": {"type": "prometheus", "uid": "d31c6998-358e-44d3-8542-ba13b1d024a5"}, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 49}, "id": 69, "panels": [], "targets": [{"datasource": {"type": "prometheus", "uid": "d31c6998-358e-44d3-8542-ba13b1d024a5"}, "refId": "A"}], "title": "Network", "type": "row"}, {"datasource": {"uid": "d31c6998-358e-44d3-8542-ba13b1d024a5"}, "description": "The average rate of requests sent to this database server per second over the selected sample period", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "links": [], "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "/s", "unitScale": true}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 50}, "id": 88, "options": {"legend": {"calcs": ["min", "max", "mean", "lastNotNull"], "displayMode": "table", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "multi", "sort": "none"}}, "pluginVersion": "10.3.3", "targets": [{"datasource": {"uid": "d31c6998-358e-44d3-8542-ba13b1d024a5"}, "exemplar": true, "expr": "label_replace(sum(irate(mongodb_network_numRequests{group_id=~\"$group_id\", cl_name=~\"$cl_name\", rs_nm=~\"$rs_nm\", instance=~\"$host.*\",  process_port=~\"$process_port\"}[$Interval])) by (instance, process_port) , \"hostname\", \"$1\", \"instance\", \"(.*)\")", "interval": "", "legendFormat": "{{hostname}}:{{process_port}}", "refId": "A"}], "title": "Network - Num Requests", "type": "timeseries"}, {"datasource": {"uid": "d31c6998-358e-44d3-8542-ba13b1d024a5"}, "description": "The average rate of physical (after any wire compression) bytes sent to this database server per second over the selected sample period", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "links": [], "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "binBps", "unitScale": true}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 50}, "id": 90, "options": {"legend": {"calcs": ["min", "max", "lastNotNull"], "displayMode": "table", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "multi", "sort": "none"}}, "pluginVersion": "10.3.3", "targets": [{"datasource": {"uid": "d31c6998-358e-44d3-8542-ba13b1d024a5"}, "exemplar": true, "expr": "label_replace(sum(irate(mongodb_network_bytesIn{group_id=~\"$group_id\", cl_name=~\"$cl_name\", rs_nm=~\"$rs_nm\", instance=~\"$host.*\",  process_port=~\"$process_port\"}[$Interval])) by (instance, process_port) , \"hostname\", \"$1\", \"instance\", \"(.*)\")", "interval": "", "legendFormat": "rx - {{hostname}}:{{process_port}}", "refId": "A"}, {"datasource": {"uid": "d31c6998-358e-44d3-8542-ba13b1d024a5"}, "expr": "label_replace(-1 * sum(irate(mongodb_network_bytesOut{group_id=~\"$group_id\", cl_name=~\"$cl_name\", rs_nm=~\"$rs_nm\", instance=~\"$host.*\",  process_port=~\"$process_port\"}[$Interval])) by (instance, process_port) , \"hostname\", \"$1\", \"instance\", \"(.*)\")", "legendFormat": "tx - {{hostname}}:{{process_port}}", "refId": "B"}], "title": "Network - Bytes In", "type": "timeseries"}, {"collapsed": true, "datasource": {"type": "prometheus", "uid": "d31c6998-358e-44d3-8542-ba13b1d024a5"}, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 58}, "id": 17, "panels": [{"datasource": {"uid": "d31c6998-358e-44d3-8542-ba13b1d024a5"}, "description": "The average rate of regular asserts raised per second over the selected sample period\n", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "links": [], "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "/s", "unitScale": true}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 75}, "id": 73, "options": {"legend": {"calcs": ["max", "lastNotNull"], "displayMode": "table", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "multi", "sort": "none"}}, "pluginVersion": "10.3.3", "targets": [{"datasource": {"uid": "d31c6998-358e-44d3-8542-ba13b1d024a5"}, "exemplar": true, "expr": "label_replace(sum(rate(mongodb_asserts_regular{group_id=~\"$group_id\", cl_name=~\"$cl_name\", rs_nm=~\"$rs_nm\", instance=~\"$host.*\",  process_port=~\"$process_port\"}[$Interval])) by (instance, process_port) , \"hostname\", \"$1\", \"instance\", \"(.*)\")", "interval": "", "legendFormat": "{{hostname}}:{{process_port}}", "refId": "A"}], "title": "Asserts - Regular", "type": "timeseries"}, {"datasource": {"uid": "d31c6998-358e-44d3-8542-ba13b1d024a5"}, "description": "The average rate of warnings per second over the selected sample period", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "links": [], "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "/s", "unitScale": true}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 75}, "id": 74, "options": {"legend": {"calcs": ["max", "lastNotNull"], "displayMode": "table", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "multi", "sort": "none"}}, "pluginVersion": "10.3.3", "targets": [{"datasource": {"uid": "d31c6998-358e-44d3-8542-ba13b1d024a5"}, "exemplar": true, "expr": "label_replace(sum(rate(mongodb_asserts_warning{group_id=~\"$group_id\", cl_name=~\"$cl_name\", rs_nm=~\"$rs_nm\", instance=~\"$host.*\",  process_port=~\"$process_port\"}[$Interval])) by (instance, process_port) , \"hostname\", \"$1\", \"instance\", \"(.*)\")", "interval": "", "legendFormat": "{{hostname}}:{{process_port}}", "refId": "A"}], "title": "Asserts - Warning", "type": "timeseries"}, {"datasource": {"uid": "d31c6998-358e-44d3-8542-ba13b1d024a5"}, "description": "The average rate of message asserts per second over the selected sample period. These are internal server errors that have a well defined text string. Stack traces are logged for these", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "links": [], "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "/s", "unitScale": true}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 83}, "id": 75, "options": {"legend": {"calcs": ["max", "lastNotNull"], "displayMode": "table", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "multi", "sort": "none"}}, "pluginVersion": "10.3.3", "targets": [{"datasource": {"uid": "d31c6998-358e-44d3-8542-ba13b1d024a5"}, "exemplar": true, "expr": "label_replace(sum(rate(mongodb_asserts_msg{group_id=~\"$group_id\", cl_name=~\"$cl_name\", rs_nm=~\"$rs_nm\", instance=~\"$host.*\",  process_port=~\"$process_port\"}[$Interval])) by (instance, process_port) , \"hostname\", \"$1\", \"instance\", \"(.*)\")", "interval": "", "legendFormat": "{{hostname}}:{{process_port}}", "refId": "A"}], "title": "Asserts - Message", "type": "timeseries"}, {"datasource": {"uid": "d31c6998-358e-44d3-8542-ba13b1d024a5"}, "description": "The average rate of user asserts per second over the selected sample period. These are errors that can be generated by a user such as out of disk space or duplicate key", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "links": [], "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "/s", "unitScale": true}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 83}, "id": 76, "options": {"legend": {"calcs": ["max", "lastNotNull"], "displayMode": "table", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "multi", "sort": "none"}}, "pluginVersion": "10.3.3", "targets": [{"datasource": {"uid": "d31c6998-358e-44d3-8542-ba13b1d024a5"}, "exemplar": true, "expr": "label_replace(sum(rate(mongodb_asserts_user{group_id=~\"$group_id\", cl_name=~\"$cl_name\", rs_nm=~\"$rs_nm\", instance=~\"$host.*\",  process_port=~\"$process_port\"}[$Interval])) by (instance, process_port) , \"hostname\", \"$1\", \"instance\", \"(.*)\")", "interval": "", "legendFormat": "{{hostname}}:{{process_port}}", "refId": "A"}], "title": "Asserts - User", "type": "timeseries"}], "targets": [{"datasource": {"type": "prometheus", "uid": "d31c6998-358e-44d3-8542-ba13b1d024a5"}, "refId": "A"}], "title": "Asserts", "type": "row"}, {"collapsed": true, "datasource": {"type": "prometheus", "uid": "d31c6998-358e-44d3-8542-ba13b1d024a5"}, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 59}, "id": 44, "panels": [{"datasource": {"uid": "d31c6998-358e-44d3-8542-ba13b1d024a5"}, "description": "The average rate of bytes per second read into WiredTiger's cache over the selected sample period.", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "links": [], "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "Bps", "unitScale": true}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 76}, "id": 77, "options": {"legend": {"calcs": ["min", "max", "mean", "lastNotNull"], "displayMode": "table", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "multi", "sort": "none"}}, "pluginVersion": "10.3.3", "targets": [{"datasource": {"uid": "d31c6998-358e-44d3-8542-ba13b1d024a5"}, "exemplar": true, "expr": "label_replace(sum(irate(mongodb_wiredTiger_cache_bytes_read_into_cache{group_id=~\"$group_id\", cl_name=~\"$cl_name\", rs_nm=~\"$rs_nm\", instance=~\"$host.*\",  process_port=~\"$process_port\"}[$Interval])) by (instance, process_port) , \"hostname\", \"$1\", \"instance\", \"(.*)\")", "interval": "", "legendFormat": "{{hostname}}:{{process_port}}", "refId": "A"}], "title": "Cache Activity - Read Into", "type": "timeseries"}, {"datasource": {"uid": "d31c6998-358e-44d3-8542-ba13b1d024a5"}, "description": "The average rate of bytes per second written from WiredTiger's cache over the selected sample period.\n", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "links": [], "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "Bps", "unitScale": true}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 76}, "id": 78, "options": {"legend": {"calcs": ["min", "max", "mean", "lastNotNull"], "displayMode": "table", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "multi", "sort": "none"}}, "pluginVersion": "10.3.3", "targets": [{"datasource": {"uid": "d31c6998-358e-44d3-8542-ba13b1d024a5"}, "exemplar": true, "expr": "label_replace(sum(irate(mongodb_wiredTiger_cache_bytes_written_from_cache{group_id=~\"$group_id\", cl_name=~\"$cl_name\", rs_nm=~\"$rs_nm\", instance=~\"$host.*\",  process_port=~\"$process_port\"}[$Interval])) by (instance, process_port) , \"hostname\", \"$1\", \"instance\", \"(.*)\")", "interval": "", "legendFormat": "{{hostname}}:{{process_port}}", "refId": "A"}], "title": "Cache Activity - Write From", "type": "timeseries"}, {"datasource": {"uid": "d31c6998-358e-44d3-8542-ba13b1d024a5"}, "description": "The number of bytes currently in the WiredTiger cache.", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "links": [], "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "bytes", "unitScale": true}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 84}, "id": 79, "options": {"legend": {"calcs": ["min", "max", "mean", "lastNotNull"], "displayMode": "table", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "multi", "sort": "none"}}, "pluginVersion": "10.3.3", "targets": [{"datasource": {"uid": "d31c6998-358e-44d3-8542-ba13b1d024a5"}, "exemplar": true, "expr": "label_replace(sum(mongodb_wiredTiger_cache_bytes_currently_in_the_cache{group_id=~\"$group_id\", cl_name=~\"$cl_name\", rs_nm=~\"$rs_nm\", instance=~\"$host.*\",  process_port=~\"$process_port\"}) by (instance, process_port) , \"hostname\", \"$1\", \"instance\", \"(.*)\")", "interval": "", "legendFormat": "{{hostname}}:{{process_port}}", "refId": "A"}], "title": "<PERSON><PERSON> - Used", "type": "timeseries"}, {"datasource": {"uid": "d31c6998-358e-44d3-8542-ba13b1d024a5"}, "description": "The number of tracked dirty bytes currently in the WiredTiger cache.", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "links": [], "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "bytes", "unitScale": true}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 84}, "id": 80, "options": {"legend": {"calcs": ["min", "max", "mean", "lastNotNull"], "displayMode": "table", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "multi", "sort": "none"}}, "pluginVersion": "10.3.3", "targets": [{"datasource": {"uid": "d31c6998-358e-44d3-8542-ba13b1d024a5"}, "exemplar": true, "expr": "label_replace(sum(mongodb_wiredTiger_cache_tracked_dirty_bytes_in_the_cache{group_id=~\"$group_id\", cl_name=~\"$cl_name\", rs_nm=~\"$rs_nm\", instance=~\"$host.*\",  process_port=~\"$process_port\"}) by (instance, process_port) , \"hostname\", \"$1\", \"instance\", \"(.*)\")", "interval": "", "legendFormat": "{{hostname}}:{{process_port}}", "refId": "A"}], "title": "<PERSON><PERSON> - <PERSON>", "type": "timeseries"}], "targets": [{"datasource": {"type": "prometheus", "uid": "d31c6998-358e-44d3-8542-ba13b1d024a5"}, "refId": "A"}], "title": "<PERSON><PERSON>", "type": "row"}, {"collapsed": true, "datasource": {"type": "prometheus", "uid": "d31c6998-358e-44d3-8542-ba13b1d024a5"}, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 60}, "id": 30, "panels": [{"datasource": {"uid": "d31c6998-358e-44d3-8542-ba13b1d024a5"}, "description": "The number of cursors that the server is maintaining for clients. Because MongoDB exhausts unused cursors, typically this value is small or zero. However, if there is a queue, stale tailable cursors, or a large number of operations this value may rise.", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "links": [], "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "red", "value": 80}]}, "unit": "none", "unitScale": true}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 56}, "id": 82, "options": {"legend": {"calcs": ["max", "lastNotNull"], "displayMode": "table", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "multi", "sort": "none"}}, "pluginVersion": "10.3.3", "targets": [{"datasource": {"uid": "d31c6998-358e-44d3-8542-ba13b1d024a5"}, "exemplar": true, "expr": "label_replace(sum(mongodb_metrics_cursor_open_total{group_id=~\"$group_id\", cl_name=~\"$cl_name\", rs_nm=~\"$rs_nm\", instance=~\"$host.*\",  process_port=~\"$process_port\"}) by (instance, process_port) , \"hostname\", \"$1\", \"instance\", \"(.*)\")", "interval": "", "legendFormat": "{{hostname}}:{{process_port}}", "refId": "A"}], "title": "Cursors - Total Open", "type": "timeseries"}, {"datasource": {"uid": "d31c6998-358e-44d3-8542-ba13b1d024a5"}, "description": "The average rate of cursors that have timed out per second over the selected sample period", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "links": [], "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "red", "value": 80}]}, "unit": "none", "unitScale": true}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 56}, "id": 83, "options": {"legend": {"calcs": ["max", "lastNotNull"], "displayMode": "table", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "multi", "sort": "none"}}, "pluginVersion": "10.3.3", "targets": [{"datasource": {"uid": "d31c6998-358e-44d3-8542-ba13b1d024a5"}, "exemplar": true, "expr": "label_replace(sum(mongodb_metrics_cursor_timedOut{group_id=~\"$group_id\", cl_name=~\"$cl_name\", rs_nm=~\"$rs_nm\", instance=~\"$host.*\",  process_port=~\"$process_port\"}) by (instance, process_port) , \"hostname\", \"$1\", \"instance\", \"(.*)\")", "interval": "", "legendFormat": "{{hostname}}:{{process_port}}", "refId": "A"}], "title": "Cursors - Timed Out", "type": "timeseries"}], "targets": [{"datasource": {"type": "prometheus", "uid": "d31c6998-358e-44d3-8542-ba13b1d024a5"}, "refId": "A"}], "title": "Cursors", "type": "row"}, {"collapsed": true, "datasource": {"type": "prometheus", "uid": "d31c6998-358e-44d3-8542-ba13b1d024a5"}, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 61}, "id": 32, "panels": [{"datasource": {"uid": "d31c6998-358e-44d3-8542-ba13b1d024a5"}, "description": "The average rate per second of documents returned by queries over the selected sample period.", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "links": [], "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "red", "value": 80}]}, "unit": "/s", "unitScale": true}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 7}, "id": 84, "options": {"legend": {"calcs": ["max", "mean", "lastNotNull"], "displayMode": "table", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "multi", "sort": "none"}}, "pluginVersion": "10.3.3", "targets": [{"datasource": {"uid": "d31c6998-358e-44d3-8542-ba13b1d024a5"}, "exemplar": true, "expr": "label_replace(sum(irate(mongodb_metrics_document_returned{group_id=~\"$group_id\", cl_name=~\"$cl_name\", rs_nm=~\"$rs_nm\", instance=~\"$host.*\",  process_port=~\"$process_port\"}[$Interval])) by (instance, process_port) , \"hostname\", \"$1\", \"instance\", \"(.*)\")", "interval": "", "legendFormat": "{{hostname}}:{{process_port}}", "refId": "A"}], "title": "Document Metrics - Returned", "type": "timeseries"}, {"datasource": {"uid": "d31c6998-358e-44d3-8542-ba13b1d024a5"}, "description": "The average rate per second of documents inserted over the selected sample period.", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "links": [], "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "red", "value": 80}]}, "unit": "/s", "unitScale": true}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 7}, "id": 87, "options": {"legend": {"calcs": ["max", "mean", "lastNotNull"], "displayMode": "table", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "multi", "sort": "none"}}, "pluginVersion": "10.3.3", "targets": [{"datasource": {"uid": "d31c6998-358e-44d3-8542-ba13b1d024a5"}, "exemplar": true, "expr": "label_replace(sum(irate(mongodb_metrics_document_inserted{group_id=~\"$group_id\", cl_name=~\"$cl_name\", rs_nm=~\"$rs_nm\", instance=~\"$host.*\",  process_port=~\"$process_port\"}[$Interval])) by (instance, process_port) , \"hostname\", \"$1\", \"instance\", \"(.*)\")", "interval": "", "legendFormat": "{{hostname}}:{{process_port}}", "refId": "A"}], "title": "Document Metrics - Inserted", "type": "timeseries"}, {"datasource": {"uid": "d31c6998-358e-44d3-8542-ba13b1d024a5"}, "description": "The average rate per second of documents updated over the selected sample period.", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "links": [], "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "red", "value": 80}]}, "unit": "/s", "unitScale": true}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 15}, "id": 86, "options": {"legend": {"calcs": ["max", "mean", "lastNotNull"], "displayMode": "table", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "multi", "sort": "none"}}, "pluginVersion": "10.3.3", "targets": [{"datasource": {"uid": "d31c6998-358e-44d3-8542-ba13b1d024a5"}, "exemplar": true, "expr": "label_replace(sum(irate(mongodb_metrics_document_updated{group_id=~\"$group_id\", cl_name=~\"$cl_name\", rs_nm=~\"$rs_nm\", instance=~\"$host.*\",  process_port=~\"$process_port\"}[$Interval])) by (instance, process_port) , \"hostname\", \"$1\", \"instance\", \"(.*)\")", "interval": "", "legendFormat": "{{hostname}}:{{process_port}}", "refId": "A"}], "title": "Document Metrics - Updated", "type": "timeseries"}, {"datasource": {"uid": "d31c6998-358e-44d3-8542-ba13b1d024a5"}, "description": "The average rate per second of documents deleted over the selected sample period.", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "links": [], "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "red", "value": 80}]}, "unit": "/s", "unitScale": true}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 15}, "id": 85, "options": {"legend": {"calcs": ["max", "mean", "lastNotNull"], "displayMode": "table", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "multi", "sort": "none"}}, "pluginVersion": "10.3.3", "targets": [{"datasource": {"uid": "d31c6998-358e-44d3-8542-ba13b1d024a5"}, "exemplar": true, "expr": "label_replace(sum(irate(mongodb_metrics_document_deleted{group_id=~\"$group_id\", cl_name=~\"$cl_name\", rs_nm=~\"$rs_nm\", instance=~\"$host.*\",  process_port=~\"$process_port\"}[$Interval])) by (instance, process_port) , \"hostname\", \"$1\", \"instance\", \"(.*)\")", "interval": "", "legendFormat": "{{hostname}}:{{process_port}}", "refId": "A"}], "title": "Document Metrics - Deleted", "type": "timeseries"}], "targets": [{"datasource": {"type": "prometheus", "uid": "d31c6998-358e-44d3-8542-ba13b1d024a5"}, "refId": "A"}], "title": "Document Metrics", "type": "row"}, {"collapsed": true, "datasource": {"type": "prometheus", "uid": "d31c6998-358e-44d3-8542-ba13b1d024a5"}, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 62}, "id": 106, "panels": [{"datasource": {"uid": "d31c6998-358e-44d3-8542-ba13b1d024a5"}, "description": "The average rate of page faults on this process per second over the selected sample period. In non-Windows environments this is hard page faults only.", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "links": [], "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "red", "value": 80}]}, "unit": "/s", "unitScale": true}, "overrides": []}, "gridPos": {"h": 8, "w": 24, "x": 0, "y": 11}, "id": 107, "options": {"legend": {"calcs": ["max", "lastNotNull"], "displayMode": "table", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "multi", "sort": "none"}}, "pluginVersion": "10.3.3", "targets": [{"datasource": {"uid": "d31c6998-358e-44d3-8542-ba13b1d024a5"}, "exemplar": true, "expr": "label_replace(sum(irate(mongodb_extra_info_page_faults{group_id=~\"$group_id\", cl_name=~\"$cl_name\", rs_nm=~\"$rs_nm\", instance=~\"$host.*\",  process_port=~\"$process_port\"}[$Interval])) by (instance, process_port) , \"hostname\", \"$1\", \"instance\", \"(.*)\")", "interval": "", "legendFormat": "{{hostname}}:{{process_port}}", "refId": "A"}], "title": "Page Faults", "type": "timeseries"}], "targets": [{"datasource": {"type": "prometheus", "uid": "d31c6998-358e-44d3-8542-ba13b1d024a5"}, "refId": "A"}], "title": "Page Faults", "type": "row"}, {"collapsed": true, "datasource": {"type": "prometheus", "uid": "d31c6998-358e-44d3-8542-ba13b1d024a5"}, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 63}, "id": 113, "panels": [{"datasource": {"uid": "d31c6998-358e-44d3-8542-ba13b1d024a5"}, "description": "The number of operations queued waiting for any lock", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "links": [], "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "none", "unitScale": true}, "overrides": []}, "gridPos": {"h": 8, "w": 8, "x": 0, "y": 80}, "id": 114, "options": {"legend": {"calcs": ["max", "lastNotNull"], "displayMode": "table", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "multi", "sort": "none"}}, "pluginVersion": "10.3.3", "targets": [{"datasource": {"uid": "d31c6998-358e-44d3-8542-ba13b1d024a5"}, "exemplar": true, "expr": "label_replace(sum(mongodb_globalLock_currentQueue_total{group_id=~\"$group_id\", cl_name=~\"$cl_name\", rs_nm=~\"$rs_nm\", instance=~\"$host.*\",  process_port=~\"$process_port\"}) by (instance, process_port) , \"hostname\", \"$1\", \"instance\", \"(.*)\")", "interval": "", "legendFormat": "{{hostname}}:{{process_port}}", "refId": "A"}], "title": "Queues - Total", "type": "timeseries"}, {"datasource": {"uid": "d31c6998-358e-44d3-8542-ba13b1d024a5"}, "description": "The number of operations queued waiting for a read lock", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "links": [], "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "none", "unitScale": true}, "overrides": []}, "gridPos": {"h": 8, "w": 8, "x": 8, "y": 80}, "id": 115, "options": {"legend": {"calcs": ["max", "lastNotNull"], "displayMode": "table", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "multi", "sort": "none"}}, "pluginVersion": "10.3.3", "targets": [{"datasource": {"uid": "d31c6998-358e-44d3-8542-ba13b1d024a5"}, "exemplar": true, "expr": "label_replace(sum(mongodb_globalLock_currentQueue_readers{group_id=~\"$group_id\", cl_name=~\"$cl_name\", rs_nm=~\"$rs_nm\", instance=~\"$host.*\",  process_port=~\"$process_port\"}) by (instance, process_port) , \"hostname\", \"$1\", \"instance\", \"(.*)\")", "interval": "", "legendFormat": "{{hostname}}:{{process_port}}", "refId": "A"}], "title": "Queues - Readers", "type": "timeseries"}, {"datasource": {"uid": "d31c6998-358e-44d3-8542-ba13b1d024a5"}, "description": "The number of operations queued waiting for a write lock", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "links": [], "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "none", "unitScale": true}, "overrides": []}, "gridPos": {"h": 8, "w": 8, "x": 16, "y": 80}, "id": 116, "options": {"legend": {"calcs": ["max", "lastNotNull"], "displayMode": "table", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "multi", "sort": "none"}}, "pluginVersion": "10.3.3", "targets": [{"datasource": {"uid": "d31c6998-358e-44d3-8542-ba13b1d024a5"}, "exemplar": true, "expr": "label_replace(sum(mongodb_globalLock_currentQueue_writers{group_id=~\"$group_id\", cl_name=~\"$cl_name\", rs_nm=~\"$rs_nm\", instance=~\"$host.*\",  process_port=~\"$process_port\"}) by (instance, process_port) , \"hostname\", \"$1\", \"instance\", \"(.*)\")", "interval": "", "legendFormat": "{{hostname}}:{{process_port}}", "refId": "A"}], "title": "Queues - Writers", "type": "timeseries"}], "targets": [{"datasource": {"type": "prometheus", "uid": "d31c6998-358e-44d3-8542-ba13b1d024a5"}, "refId": "A"}], "title": "Queues", "type": "row"}, {"collapsed": false, "datasource": {"type": "prometheus", "uid": "d31c6998-358e-44d3-8542-ba13b1d024a5"}, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 64}, "id": 118, "panels": [], "targets": [{"datasource": {"type": "prometheus", "uid": "d31c6998-358e-44d3-8542-ba13b1d024a5"}, "refId": "A"}], "title": "Scan and Order", "type": "row"}, {"datasource": {"uid": "d31c6998-358e-44d3-8542-ba13b1d024a5"}, "description": "The average rate per second over the selected sample period of queries that return sorted results that cannot perform the sort operation using an index.", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "links": [], "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "/s", "unitScale": true}, "overrides": []}, "gridPos": {"h": 8, "w": 24, "x": 0, "y": 65}, "id": 119, "options": {"legend": {"calcs": ["min", "max", "lastNotNull"], "displayMode": "table", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "multi", "sort": "none"}}, "pluginVersion": "10.3.3", "targets": [{"datasource": {"uid": "d31c6998-358e-44d3-8542-ba13b1d024a5"}, "exemplar": true, "expr": "label_replace(sum(irate(mongodb_metrics_operation_scanAndOrder{group_id=~\"$group_id\", cl_name=~\"$cl_name\", rs_nm=~\"$rs_nm\", instance=~\"$host.*\",  process_port=~\"$process_port\"}[$Interval])) by (instance, process_port) , \"hostname\", \"$1\", \"instance\", \"(.*)\")", "interval": "", "legendFormat": "{{hostname}}:{{process_port}}", "refId": "A"}], "title": "Scan and Order", "type": "timeseries"}, {"collapsed": true, "datasource": {"type": "prometheus", "uid": "d31c6998-358e-44d3-8542-ba13b1d024a5"}, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 73}, "id": 123, "panels": [{"datasource": {"type": "prometheus", "uid": "d31c6998-358e-44d3-8542-ba13b1d024a5"}, "description": "The number of read tickets available to the WiredTiger storage engine. Read tickets represent the number of concurrent read operations allowed into the storage engine. When this value reaches zero new read requests may queue until a read ticket becomes available.", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "links": [], "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "red", "value": 80}]}, "unit": "none", "unitScale": true}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 15}, "id": 120, "options": {"legend": {"calcs": ["min", "max", "lastNotNull"], "displayMode": "table", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "multi", "sort": "none"}}, "pluginVersion": "10.3.3", "targets": [{"datasource": {"uid": "d31c6998-358e-44d3-8542-ba13b1d024a5"}, "editorMode": "code", "exemplar": true, "expr": "label_replace(sum(mongodb_wiredTiger_concurrentTransactions_read_available{group_id=~\"$group_id\", cl_name=~\"$cl_name\", rs_nm=~\"$rs_nm\", instance=~\"$host.*\",  process_port=~\"$process_port\"}) by (instance, process_port) , \"hostname\", \"$1\", \"instance\", \"(.*)\")", "interval": "", "legendFormat": "{{hostname}}:{{process_port}}", "range": true, "refId": "A"}], "title": "Tickets Available - Reads", "type": "timeseries"}, {"datasource": {"uid": "d31c6998-358e-44d3-8542-ba13b1d024a5"}, "description": "The number of write tickets available to the WiredTiger storage engine. Write tickets represent the number of concurrent write operations allowed into the storage engine. When this value reaches zero new write requests may queue until a write ticket becomes available.", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "links": [], "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "red", "value": 80}]}, "unit": "none", "unitScale": true}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 15}, "id": 121, "options": {"legend": {"calcs": ["min", "max", "lastNotNull"], "displayMode": "table", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "multi", "sort": "none"}}, "pluginVersion": "10.3.3", "targets": [{"datasource": {"uid": "d31c6998-358e-44d3-8542-ba13b1d024a5"}, "exemplar": true, "expr": "label_replace(sum(mongodb_wiredTiger_concurrentTransactions_write_available{group_id=~\"$group_id\", cl_name=~\"$cl_name\", rs_nm=~\"$rs_nm\", instance=~\"$host.*\",  process_port=~\"$process_port\"}) by (instance, process_port) , \"hostname\", \"$1\", \"instance\", \"(.*)\")", "interval": "", "legendFormat": "{{hostname}}:{{process_port}}", "refId": "A"}], "title": "Tickets Available - Writes", "type": "timeseries"}], "targets": [{"datasource": {"type": "prometheus", "uid": "d31c6998-358e-44d3-8542-ba13b1d024a5"}, "refId": "A"}], "title": "Tickets Available", "type": "row"}], "refresh": "30s", "schemaVersion": 39, "tags": [], "templating": {"list": [{"auto": true, "auto_count": 30, "auto_min": "1m", "current": {"selected": false, "text": "auto", "value": "$__auto_interval_Interval"}, "hide": 0, "name": "Interval", "options": [{"selected": true, "text": "auto", "value": "$__auto_interval_Interval"}, {"selected": false, "text": "30s", "value": "30s"}, {"selected": false, "text": "1m", "value": "1m"}, {"selected": false, "text": "5m", "value": "5m"}, {"selected": false, "text": "1h", "value": "1h"}, {"selected": false, "text": "1d", "value": "1d"}], "query": "30s,1m,5m,1h,1d", "queryValue": "", "refresh": 2, "skipUrlSync": false, "type": "interval"}, {"current": {"selected": false, "text": "prometheus", "value": "d31c6998-358e-44d3-8542-ba13b1d024a5"}, "hide": 0, "includeAll": false, "multi": false, "name": "Datasource", "options": [], "query": "prometheus", "queryValue": "", "refresh": 1, "regex": "", "skipUrlSync": false, "type": "datasource"}, {"current": {"selected": false, "text": "Medical-mongo-metrics", "value": "Medical-mongo-metrics"}, "datasource": {"type": "prometheus", "uid": "$Datasource"}, "definition": "label_values(mongodb_up, job)", "hide": 0, "includeAll": false, "multi": false, "name": "job", "options": [], "query": {"query": "label_values(mongodb_up, job)", "refId": "thanos-job-Variable-Query"}, "refresh": 2, "regex": "", "skipUrlSync": false, "sort": 0, "tagValuesQuery": "", "tagsQuery": "", "type": "query", "useTags": false}, {"current": {"selected": false, "text": "622816f117b23174b353b2e3", "value": "622816f117b23174b353b2e3"}, "datasource": {"type": "prometheus", "uid": "$Datasource"}, "definition": "label_values(mongodb_up{job=\"$job\"}, group_id)", "hide": 0, "includeAll": false, "label": "Group Id", "multi": false, "name": "group_id", "options": [], "query": {"query": "label_values(mongodb_up{job=\"$job\"}, group_id)", "refId": "thanos-group_id-Variable-Query"}, "refresh": 1, "regex": "", "skipUrlSync": false, "sort": 5, "tagValuesQuery": "", "tagsQuery": "", "type": "query", "useTags": false}, {"current": {"selected": false, "text": "aws-bureau", "value": "aws-bureau"}, "datasource": {"type": "prometheus", "uid": "$Datasource"}, "definition": "label_values(mongodb_up{group_id='$group_id'}, cl_name)", "hide": 0, "includeAll": false, "label": "Cluster Name", "multi": false, "name": "cl_name", "options": [], "query": {"query": "label_values(mongodb_up{group_id='$group_id'}, cl_name)", "refId": "thanos-cl_name-Variable-Query"}, "refresh": 2, "regex": "", "skipUrlSync": false, "sort": 5, "tagValuesQuery": "", "tagsQuery": "", "type": "query", "useTags": false}, {"current": {"selected": true, "text": ["All"], "value": ["$__all"]}, "datasource": {"type": "prometheus", "uid": "$Datasource"}, "definition": "label_values(mongodb_up{group_id='$group_id', cl_name='$cl_name'}, rs_nm)", "hide": 0, "includeAll": true, "label": "ReplicaSet Name", "multi": true, "name": "rs_nm", "options": [], "query": {"query": "label_values(mongodb_up{group_id='$group_id', cl_name='$cl_name'}, rs_nm)", "refId": "thanos-rs_nm-Variable-Query"}, "refresh": 1, "regex": "", "skipUrlSync": false, "sort": 5, "tagValuesQuery": "", "tagsQuery": "", "type": "query", "useTags": false}, {"current": {"selected": true, "text": ["All"], "value": ["$__all"]}, "datasource": {"type": "prometheus", "uid": "$Datasource"}, "definition": "label_values(mongodb_up{group_id='$group_id', cl_name='$cl_name',rs_nm='$rs_nm'},instance)", "hide": 0, "includeAll": true, "label": "Host", "multi": true, "name": "host", "options": [], "query": {"query": "label_values(mongodb_up{group_id='$group_id', cl_name='$cl_name',rs_nm='$rs_nm'},instance)", "refId": "thanos-host-Variable-Query"}, "refresh": 2, "regex": "", "skipUrlSync": false, "sort": 5, "tagValuesQuery": "", "tagsQuery": "", "type": "query", "useTags": false}, {"current": {"selected": true, "text": ["All"], "value": ["$__all"]}, "datasource": {"type": "prometheus", "uid": "$Datasource"}, "definition": "label_values(mongodb_up{group_id='$group_id', cl_name='$cl_name',rs_nm='$rs_nm'},process_port)", "description": "Only applicable for process level metrics", "hide": 0, "includeAll": true, "label": "Process Port", "multi": true, "name": "process_port", "options": [], "query": {"query": "label_values(mongodb_up{group_id='$group_id', cl_name='$cl_name',rs_nm='$rs_nm'},process_port)", "refId": "thanos-process_port-Variable-Query"}, "refresh": 1, "regex": "", "skipUrlSync": false, "sort": 5, "tagValuesQuery": "", "tagsQuery": "", "type": "query", "useTags": false}]}, "time": {"from": "now-1h", "to": "now"}, "timepicker": {"refresh_intervals": ["5s", "10s", "30s", "1m", "5m", "15m", "30m", "1h", "2h", "1d"]}, "timezone": "", "title": "MongoDB Atlas System Metrics", "uid": "W0lo7Gx7z123", "version": 3, "weekStart": ""}