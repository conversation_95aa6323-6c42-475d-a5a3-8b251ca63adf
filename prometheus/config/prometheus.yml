global:
  scrape_interval: 15s # Default is every 1 minute.
  evaluation_interval: 15s # The default is every 1 minute.
  scrape_timeout: 15s
  # scrape_timeout is set to the global default (10s).

# Alertmanager configuration
# alerting:
#   alertmanagers:
#   - static_configs:
#     - targets: ['172.1.5.220:9093']
# - alertmanager:9093

# Load rules once and periodically evaluate them according to the global 'evaluation_interval'.
# rule_files:
#   - "node_down.yml"
# - "first_rules.yml"
# - "second_rules.yml"

# A scrape configuration containing exactly one endpoint to scrape:
# Here it's Prometheus itself.

scrape_configs:
  # The job name is added as a label `job=<job_name>` to any timeseries scraped from this config.

  - job_name: "prometheus"
    static_configs:
      - targets: ["172.17.0.241:9091"]

  - job_name: "node_exporter"
    file_sd_configs:
      - refresh_interval: 15s
        files:
          - node-exporter.yml

  - job_name: "Medical-mongo-metrics"
    metrics_path: /metrics
    scheme: https
    basic_auth:
      username: "prom_user_622816f117b23174b353b2e3"
      password: "6aX3OfAjA6JBktNm"
    http_sd_configs:
      - url: https://cloud.mongodb.com/prometheus/v1.0/groups/622816f117b23174b353b2e3/discovery?targetScheme=PRIVATE
        refresh_interval: 15s
        basic_auth:
          username: "prom_user_622816f117b23174b353b2e3"
          password: "6aX3OfAjA6JBktNm"
    file_sd_configs:
      - refresh_interval: 15s
        files:
          - mongodb-exporter.yml

  - job_name: "neox_metrics"
    static_configs:
      - targets: ["172.17.0.241:7333"]