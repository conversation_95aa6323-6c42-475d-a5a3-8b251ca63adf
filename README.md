# Grafana in NeoX #

The usage of Grafana in NeoX.

## Set up ##

```bash
# Create external network, then use it in .env.
docker network create --driver bridge ${external_network_name}

# Make sure docker-compose has been installed.
# https://docs.docker.com/compose/install/
docker-compose up -d # Start all service.
# service_name Not container_name !!!
docker-compose restart ${service_name} 
docker-compose stop ${service_name}
docker-compose start ${service_name}
docker-compose logs ${service_name}
docker-compose rm -f ${service_name} # remove container, not data !!!
```

## Notice ##

* In the docker-compose.yml, the `data` directories specified in the `volumes` section need to be manually created in advance.