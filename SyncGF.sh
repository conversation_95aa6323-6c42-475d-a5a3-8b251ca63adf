#!/bin/bash

# 功能: 同步 Git 仓库文件到指定目录，并创建特定的子目录结构。
# 用法: ./SyncGF.sh <source_directory> <destination_directory>

# --- 脚本初始化 ---
# 如果任何命令执行失败，则立即退出脚本
set -e

# --- 1. 参数和路径校验 ---
SRC_PATH="$1"
DEST_PATH="$2"

# 检查是否提供了两个参数
if [ -z "$SRC_PATH" ] || [ -z "$DEST_PATH" ]; then
  echo "错误: 必须提供源路径和目标路径两个参数。" >&2
  echo "用法: $0 <源路径> <目标路径>" >&2
  exit 1
fi

# 检查源路径是否存在且为目录
if [ ! -d "$SRC_PATH" ]; then
  echo "错误: 源路径 '$SRC_PATH' 不是一个有效的目录或不存在。" >&2
  exit 1
fi

# 获取源路径和目标路径的绝对路径
SRC_PATH="$(cd "$SRC_PATH" && pwd)"
# 如果目标路径不存在，则先不转换，在创建后再转换
if [ -d "$DEST_PATH" ]; then
    DEST_PATH="$(cd "$DEST_PATH" && pwd)"
fi

# 检查目标路径是否存在，如果不存在则创建
if [ ! -d "$DEST_PATH" ]; then
  echo "信息: 目标路径 '$DEST_PATH' 不存在，现在开始创建..."
  if ! mkdir -p "$DEST_PATH"; then
    echo "错误: 创建目标目录 '$DEST_PATH' 失败。" >&2
    exit 1
  fi
  echo "信息: 目标目录 '$DEST_PATH' 创建成功。"
  DEST_PATH="$(cd "$DEST_PATH" && pwd)"
fi


# --- 2. 从 Git 仓库拉取最新代码 ---
echo "信息: 进入源目录 '$SRC_PATH' 并执行 'git pull'..."
cd "$SRC_PATH"
if ! git pull; then
  echo "错误: 在 '$SRC_PATH' 目录中执行 'git pull' 失败。" >&2
  exit 1
fi
echo "信息: 'git pull' 命令执行成功。"


# --- 3. 同步文件 ---
echo "信息: 开始将文件从 '$SRC_PATH' 同步到 '$DEST_PATH'..."
echo "信息: 将排除 .git/, .gitignore, README.md, 和 dashboard/ ..."
if ! rsync -av --delete --exclude ".git" --exclude ".gitignore" --exclude "README.md" --exclude "dashboard/" "$SRC_PATH/" "$DEST_PATH/"; then
    echo "错误: 使用 rsync 从 '$SRC_PATH' 同步文件到 '$DEST_PATH' 失败。" >&2
    exit 1
fi
echo "信息: 文件同步成功。"


# --- 4. 在目标路径下创建指定目录 ---
echo "信息: 在目标目录 '$DEST_PATH' 中创建指定的子目录..."
cd "$DEST_PATH"

# 定义需要创建的目录列表
DIRS_TO_CREATE=(
    "grafana/data"
    "prometheus/data"
    "redis/data"
    "loki/data/retention"
    "loki/log"
    "loki/tmp"
    "timescaledb/data"
)

# 循环创建目录
for dir in "${DIRS_TO_CREATE[@]}"; do
    if ! mkdir -p "$dir"; then
        echo "错误: 在 '$DEST_PATH' 中创建子目录 '$dir' 失败。" >&2
        # 不需要退出，因为 set -e 会处理
    else
        echo "  - 已创建或已存在: $dir"
    fi
done
echo "信息: 所有指定的子目录均已成功创建。"

echo ""
echo "脚本 'SyncGF.sh' 执行完毕。"
exit 0